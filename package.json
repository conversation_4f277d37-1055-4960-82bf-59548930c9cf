{"dependencies": {"@ericblade/quagga2": "^1.8.4", "@zxing/browser": "^0.1.5", "@zxing/library": "^0.21.3", "echarts": "^5.6.0", "qr-scanner": "^1.4.2", "quagga": "^0.12.1", "vant": "^2.13.6"}, "devDependencies": {"autoprefixer": "^9.8.6", "cross-env": "^7.0.3", "css-loader": "^4.2.2", "postcss": "^7.0.39", "postcss-loader": "^4.1.0", "tailwindcss": "npm:@tailwindcss/postcss7-compat@^2.2.17", "vconsole": "^3.15.1"}, "scripts": {"dev": "cross-env NODE_ENV=development uni-app serve", "build": "cross-env NODE_ENV=production uni-app build", "build:h5": "cross-env NODE_ENV=production UNI_PLATFORM=h5 uni-app build", "build:app-plus": "cross-env NODE_ENV=production UNI_PLATFORM=app-plus uni-app build"}}