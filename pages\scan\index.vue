<template>
  <view class="container">
    <!-- 扫码器容器 -->
    <view class="scan-box">
      <div
        :id="scannerId"
        class="code-reader"
        :style="{ width: '100%', height: 'auto' }"
      ></div>

      <!-- 扫描提示 -->
      <view class="tip-text" v-if="!isScanning">
        <view>{{ scanTipText }}</view>
        <view class="code-tip">
          <span class="mode-restriction">{{ modeRestrictionText }}</span>
        </view>
      </view>
    </view>

    <!-- 成功效果 -->
    <view v-if="showSuccessEffect" class="success-effect">
      <view class="success-icon">
        <uni-icons type="checkmarkempty" size="60" color="#fff"></uni-icons>
      </view>
      <view class="success-text">扫码成功</view>
    </view>

    <!-- 控制按钮 -->
    <div class="control-buttons">
      <view class="camera-toggle-btn" @click="toggleCamera" v-if="isScanning">
        <text>切换摄像头</text>
      </view>

      <view
        class="flashlight-btn"
        @click="toggleFlashlight"
        v-if="isScanning && supportsTorch"
      >
        <text>{{ flashlightOn ? "关闭闪光灯" : "开启闪光灯" }}</text>
      </view>

      <!-- 手动输入按钮 (仅走访扫码显示) -->
      <view
        class="manual-input-btn"
        @click="showManualInput"
        v-if="scanMode === 'visit'"
      >
        <text>手动输入</text>
      </view>
    </div>

    <!-- 手动输入弹窗 (仅走访扫码) -->
    <view v-if="showManualInputDialog" class="manual-input-overlay">
      <view class="manual-input-dialog">
        <view class="dialog-title">手动输入条形码</view>
        <input
          v-model="manualCode"
          placeholder="请输入条形码"
          class="manual-input"
          type="text"
          @input="validateManualCode"
        />
        <view class="dialog-buttons">
          <button class="cancel-btn" @click="closeManualInput">取消</button>
          <button
            class="confirm-btn"
            @click="submitManualCode"
            :disabled="!isValidManualCode"
          >
            确定
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { Html5Qrcode, Html5QrcodeSupportedFormats } from "html5-qrcode";
import { getQrcode } from "@/api";

export default {
  data() {
    return {
      html5QrCode: null,
      isScanning: false,
      showSuccessEffect: false,
      lastScanTime: 0,
      isProcessing: false,
      isDestroyed: false,
      isInitializing: false,
      flashlightOn: false,
      supportsTorch: false,
      currentCameraId: null,
      availableCameras: [],
      currentCameraIndex: 0,

      // 扫码模式：'visit'(走访) 或 'inspect'(巡视)
      scanMode: "visit",

      // 手动输入相关 (仅走访扫码)
      manualCode: "",
      isValidManualCode: false,
      showManualInputDialog: false,
    };
  },

  computed: {
    // 扫码器ID
    scannerId() {
      return this.scanMode === "visit"
        ? "visit-barcode-reader"
        : "inspect-qr-reader";
    },

    // 扫码类型参数
    scanType() {
      return this.scanMode === "visit" ? "1" : "0";
    },

    // 扫描提示文本
    scanTipText() {
      return this.scanMode === "visit"
        ? "将条形码放入框内进行扫描"
        : "将二维码放入框内进行扫描";
    },

    // 模式限制文本
    modeRestrictionText() {
      return this.scanMode === "visit"
        ? "走访专用条形码扫描"
        : "巡视专用二维码扫描";
    },

    // 扫码配置
    scanConfig() {
      if (this.scanMode === "visit") {
        // 条形码扫描配置
        return {
          fps: 10,
          qrbox: { width: 350, height: 150 }, // 条形码通常是矩形
          aspectRatio: 1.0,
          disableFlip: false,
        };
      } else {
        // 二维码扫描配置
        return {
          fps: 10,
          qrbox: { width: 250, height: 250 }, // 二维码是正方形
          aspectRatio: 1.0,
          disableFlip: false,
        };
      }
    },

    // 支持的扫码格式
    supportedFormats() {
      if (this.scanMode === "visit") {
        // 走访扫码支持多种条形码格式
        return [
          Html5QrcodeSupportedFormats.EAN_13,
          Html5QrcodeSupportedFormats.EAN_8,
          Html5QrcodeSupportedFormats.UPC_A,
          Html5QrcodeSupportedFormats.UPC_E,
          Html5QrcodeSupportedFormats.CODE_128,
          Html5QrcodeSupportedFormats.CODE_39,
          Html5QrcodeSupportedFormats.CODE_93,
          Html5QrcodeSupportedFormats.ITF,
          Html5QrcodeSupportedFormats.CODABAR,
        ];
      } else {
        // 巡视扫码只支持二维码
        return [Html5QrcodeSupportedFormats.QR_CODE];
      }
    },

    // 跳转页面URL
    targetPageUrl() {
      return this.scanMode === "visit"
        ? "/pages/visit-detail/index"
        : "/pages/inspect-detail/index";
    },

    // URL参数名称
    codeParamName() {
      return this.scanMode === "visit" ? "barcode" : "qrcode";
    },
  },

  onLoad(options) {
    console.log(`[UnifiedScan] 页面加载，参数:`, options);

    // 从URL参数获取扫码模式
    if (options.mode) {
      this.scanMode = options.mode;
    }

    console.log(`[UnifiedScan] 扫码模式: ${this.scanMode}`);
  },

  mounted() {
    console.log(`[UnifiedScan] mounted - 模式: ${this.scanMode}`);
    this.initializeScanner();
  },

  beforeUnmount() {
    console.log(`[UnifiedScan] beforeUnmount`);
    this.isDestroyed = true;
    this.cleanup();
  },

  onUnload() {
    console.log(`[UnifiedScan] onUnload`);
    this.isDestroyed = true;
    this.cleanup();
  },

  onHide() {
    console.log(`[UnifiedScan] onHide`);
    this.stopScanning();
  },

  onShow() {
    console.log(`[UnifiedScan] onShow`);
    if (!this.isScanning && !this.isDestroyed) {
      setTimeout(() => {
        this.initializeScanner();
      }, 500);
    }
  },

  methods: {
    // 初始化扫码器
    async initializeScanner() {
      // 防止重复初始化
      if (this.isInitializing) {
        console.log(`[UnifiedScan] 正在初始化中，跳过重复调用`);
        return;
      }

      try {
        console.log(`[UnifiedScan] 初始化扫码器 - 模式: ${this.scanMode}`);
        this.isInitializing = true;

        // 如果已经有实例，先清理
        if (this.html5QrCode) {
          await this.cleanup();
        }

        // 创建Html5Qrcode实例
        this.html5QrCode = new Html5Qrcode(this.scannerId, {
          formatsToSupport: this.supportedFormats,
          verbose: false,
        });

        // 获取可用摄像头
        await this.getCameras();

        // 启动扫描
        await this.startScanning();
      } catch (error) {
        console.error(`[UnifiedScan] 初始化扫码器失败:`, error);
        uni.showToast({
          title: "扫码器初始化失败",
          icon: "none",
          duration: 2000,
        });
      } finally {
        this.isInitializing = false;
      }
    },

    // 获取可用摄像头
    async getCameras() {
      try {
        const cameras = await Html5Qrcode.getCameras();
        this.availableCameras = cameras || [];

        console.log(`[UnifiedScan] 获取到摄像头列表:`, this.availableCameras);

        // 多重策略优先选择后置摄像头
        let backCameraIndex = -1;

        // 策略1：明确包含"back"或"rear"或"environment"关键字的摄像头
        backCameraIndex = this.availableCameras.findIndex(
          (camera) =>
            camera.label &&
            (camera.label.toLowerCase().includes("back") ||
              camera.label.toLowerCase().includes("rear") ||
              camera.label.toLowerCase().includes("environment"))
        );

        // 策略2：如果策略1失败，选择索引为1的摄像头（通常是后置）
        if (backCameraIndex === -1 && this.availableCameras.length > 1) {
          backCameraIndex = 1;
        }

        // 策略3：如果都失败，使用第一个可用摄像头
        if (backCameraIndex === -1 && this.availableCameras.length > 0) {
          backCameraIndex = 0;
        }

        if (backCameraIndex !== -1) {
          this.currentCameraIndex = backCameraIndex;
          this.currentCameraId = this.availableCameras[backCameraIndex].id;
          console.log(
            `[UnifiedScan] 选择摄像头:`,
            this.availableCameras[backCameraIndex]
          );
        } else {
          console.warn(`[UnifiedScan] 未找到可用摄像头`);
        }
      } catch (error) {
        console.error(`[UnifiedScan] 获取摄像头失败:`, error);
        uni.showToast({
          title: "获取摄像头失败",
          icon: "none",
          duration: 2000,
        });
      }
    },

    // 开始扫描
    async startScanning() {
      if (this.isScanning || this.isDestroyed || !this.html5QrCode) {
        return;
      }

      try {
        console.log(`[UnifiedScan] 开始扫描 - 模式: ${this.scanMode}`);
        this.isScanning = true;

        // 扫码成功回调
        const successCallback = (decodedText, decodedResult) => {
          this.onScanSuccess(decodedText, decodedResult);
        };

        // 扫码失败回调（通常可以忽略）
        const errorCallback = (errorMessage) => {
          // 静默处理扫描错误，避免控制台日志过多
        };

        // 尝试启动扫描，使用回退策略
        await this.tryStartScanning(successCallback, errorCallback);

        console.log(`[UnifiedScan] 扫描已启动`);

        // 检查是否支持闪光灯
        this.checkTorchSupport();
      } catch (error) {
        console.error(`[UnifiedScan] 启动扫描失败:`, error);
        this.isScanning = false;

        uni.showToast({
          title: "启动扫描失败，请检查摄像头权限",
          icon: "none",
          duration: 3000,
        });
      }
    },

    // 尝试启动扫描，使用回退策略
    async tryStartScanning(successCallback, errorCallback) {
      // 策略1：使用选定的摄像头设备ID
      if (this.currentCameraId) {
        try {
          console.log(
            `[UnifiedScan] 尝试使用选定摄像头:`,
            this.currentCameraId
          );
          await this.html5QrCode.start(
            { deviceId: { exact: this.currentCameraId } },
            this.scanConfig,
            successCallback,
            errorCallback
          );
          return; // 成功则返回
        } catch (error) {
          console.warn(
            `[UnifiedScan] 选定摄像头启动失败，尝试其他摄像头:`,
            error
          );

          // 如果当前摄像头失败，尝试其他摄像头
          await this.tryOtherCameras(successCallback, errorCallback);
          return;
        }
      }

      // 策略2：使用环境摄像头约束
      try {
        console.log(`[UnifiedScan] 尝试使用环境摄像头约束`);
        await this.html5QrCode.start(
          { facingMode: "environment" },
          this.scanConfig,
          successCallback,
          errorCallback
        );
        return;
      } catch (error) {
        console.warn(`[UnifiedScan] 环境摄像头约束失败:`, error);
      }

      // 策略3：使用默认摄像头
      try {
        console.log(`[UnifiedScan] 尝试使用默认摄像头`);
        await this.html5QrCode.start(
          { facingMode: "user" },
          this.scanConfig,
          successCallback,
          errorCallback
        );
      } catch (error) {
        console.error(`[UnifiedScan] 所有摄像头启动策略都失败:`, error);
        throw error;
      }
    },

    // 尝试其他摄像头
    async tryOtherCameras(successCallback, errorCallback) {
      for (let i = 0; i < this.availableCameras.length; i++) {
        if (i === this.currentCameraIndex) continue; // 跳过已经尝试过的摄像头

        try {
          const camera = this.availableCameras[i];
          console.log(`[UnifiedScan] 尝试摄像头 ${i}:`, camera);

          await this.html5QrCode.start(
            { deviceId: { exact: camera.id } },
            this.scanConfig,
            successCallback,
            errorCallback
          );

          // 成功则更新当前摄像头
          this.currentCameraIndex = i;
          this.currentCameraId = camera.id;
          console.log(`[UnifiedScan] 成功切换到摄像头 ${i}`);
          return;
        } catch (error) {
          console.warn(`[UnifiedScan] 摄像头 ${i} 启动失败:`, error);
        }
      }

      throw new Error("所有摄像头都无法启动");
    },

    // 停止扫描
    async stopScanning() {
      if (!this.isScanning || !this.html5QrCode) {
        return;
      }

      try {
        console.log(`[UnifiedScan] 停止扫描`);
        await this.html5QrCode.stop();
        this.isScanning = false;
        this.flashlightOn = false;
        this.supportsTorch = false;
      } catch (error) {
        console.error(`[UnifiedScan] 停止扫描失败:`, error);
      }
    },

    // 扫码成功处理
    async onScanSuccess(decodedText, decodedResult) {
      const currentTime = Date.now();

      // 防止重复扫描（500ms内）
      if (currentTime - this.lastScanTime < 500 || this.isProcessing) {
        return;
      }

      this.lastScanTime = currentTime;
      this.isProcessing = true;

      try {
        console.log(`[UnifiedScan] 扫码成功:`, decodedText);

        // 验证扫码结果格式
        if (!this.validateScanResult(decodedText)) {
          const codeType = this.scanMode === "visit" ? "条形码" : "二维码";
          uni.showToast({
            title: `请扫描有效的${codeType}`,
            icon: "none",
            duration: 2000,
          });
          return;
        }

        // 显示成功效果
        this.showSuccessEffect = true;
        setTimeout(() => {
          this.showSuccessEffect = false;
        }, 1500);

        // 调用业务API验证扫码结果
        const response = await getQrcode({
          qrcode: decodedText,
          type: this.scanType,
        });

        if (response && response.code === 200) {
          const codeType = this.scanMode === "visit" ? "条形码" : "二维码";
          console.log(`[UnifiedScan] ${codeType}验证成功:`, response.data);

          // 停止扫描
          await this.stopScanning();

          // 跳转到下一个页面，传递必要参数
          setTimeout(() => {
            uni.navigateTo({
              url: `${this.targetPageUrl}?${
                this.codeParamName
              }=${encodeURIComponent(decodedText)}&data=${encodeURIComponent(
                JSON.stringify(response.data)
              )}`,
            });
          }, 1000);
        } else {
          const codeType = this.scanMode === "visit" ? "条形码" : "二维码";
          throw new Error(response?.msg || `${codeType}验证失败`);
        }
      } catch (error) {
        console.error(`[UnifiedScan] 处理扫码结果失败:`, error);

        const codeType = this.scanMode === "visit" ? "条形码" : "二维码";
        uni.showToast({
          title: error.message || `${codeType}无效`,
          icon: "none",
          duration: 2000,
        });
      } finally {
        // 延迟重置处理状态，避免快速重复扫描
        setTimeout(() => {
          this.isProcessing = false;
        }, 1000);
      }
    },

    // 验证扫码结果格式
    validateScanResult(code) {
      if (!code || typeof code !== "string") {
        return false;
      }

      if (this.scanMode === "visit") {
        // 走访扫码：验证条形码格式（通常是数字）
        return /^[0-9]{8,}$/.test(code.trim());
      } else {
        // 巡视扫码：二维码格式验证（更宽松）
        return code.trim().length > 0;
      }
    },

    // 检查闪光灯支持
    async checkTorchSupport() {
      try {
        if (this.html5QrCode && this.html5QrCode.getRunningTrackCapabilities) {
          const capabilities = this.html5QrCode.getRunningTrackCapabilities();
          this.supportsTorch = capabilities && capabilities.torch === true;
          console.log(`[UnifiedScan] 闪光灯支持:`, this.supportsTorch);
        }
      } catch (error) {
        console.warn(`[UnifiedScan] 检查闪光灯支持失败:`, error);
        this.supportsTorch = false;
      }
    },

    // 切换摄像头
    async toggleCamera() {
      if (!this.isScanning || this.availableCameras.length <= 1) {
        return;
      }

      try {
        console.log(`[UnifiedScan] 切换摄像头`);

        // 停止当前扫描
        await this.stopScanning();

        // 切换到下一个摄像头
        this.currentCameraIndex =
          (this.currentCameraIndex + 1) % this.availableCameras.length;
        this.currentCameraId =
          this.availableCameras[this.currentCameraIndex].id;

        console.log(
          `[UnifiedScan] 切换到摄像头:`,
          this.availableCameras[this.currentCameraIndex]
        );

        // 重新启动扫描
        await this.startScanning();
      } catch (error) {
        console.error(`[UnifiedScan] 切换摄像头失败:`, error);
        uni.showToast({
          title: "切换摄像头失败",
          icon: "none",
          duration: 2000,
        });
      }
    },

    // 切换闪光灯
    async toggleFlashlight() {
      if (!this.supportsTorch || !this.html5QrCode) {
        return;
      }

      try {
        if (this.flashlightOn) {
          await this.html5QrCode.applyVideoConstraints({
            advanced: [{ torch: false }],
          });
          this.flashlightOn = false;
          console.log(`[UnifiedScan] 闪光灯已关闭`);
        } else {
          await this.html5QrCode.applyVideoConstraints({
            advanced: [{ torch: true }],
          });
          this.flashlightOn = true;
          console.log(`[UnifiedScan] 闪光灯已开启`);
        }
      } catch (error) {
        console.error(`[UnifiedScan] 切换闪光灯失败:`, error);
        uni.showToast({
          title: "闪光灯操作失败",
          icon: "none",
          duration: 2000,
        });
      }
    },

    // 显示手动输入弹窗 (仅走访扫码)
    showManualInput() {
      if (this.scanMode !== "visit") {
        return;
      }
      this.showManualInputDialog = true;
      this.manualCode = "";
      this.isValidManualCode = false;
    },

    // 关闭手动输入弹窗
    closeManualInput() {
      this.showManualInputDialog = false;
      this.manualCode = "";
      this.isValidManualCode = false;
    },

    // 验证手动输入的条形码
    validateManualCode() {
      const code = this.manualCode.trim();
      this.isValidManualCode = /^[0-9]{8,}$/.test(code);
    },

    // 提交手动输入的条形码
    async submitManualCode() {
      if (!this.isValidManualCode || this.isProcessing) {
        return;
      }

      this.isProcessing = true;

      try {
        console.log(`[UnifiedScan] 手动输入条形码:`, this.manualCode);

        // 关闭弹窗
        this.closeManualInput();

        // 显示成功效果
        this.showSuccessEffect = true;
        setTimeout(() => {
          this.showSuccessEffect = false;
        }, 1500);

        // 调用业务API验证条形码
        const response = await getQrcode({
          qrcode: this.manualCode,
          type: this.scanType,
        });

        if (response && response.code === 200) {
          console.log(`[UnifiedScan] 手动输入条形码验证成功:`, response.data);

          // 停止扫描
          await this.stopScanning();

          // 跳转到下一个页面，传递必要参数
          setTimeout(() => {
            uni.navigateTo({
              url: `${this.targetPageUrl}?${
                this.codeParamName
              }=${encodeURIComponent(
                this.manualCode
              )}&data=${encodeURIComponent(JSON.stringify(response.data))}`,
            });
          }, 1000);
        } else {
          throw new Error(response?.msg || "条形码验证失败");
        }
      } catch (error) {
        console.error(`[UnifiedScan] 手动输入条形码处理失败:`, error);

        uni.showToast({
          title: error.message || "条形码无效",
          icon: "none",
          duration: 2000,
        });
      } finally {
        // 延迟重置处理状态
        setTimeout(() => {
          this.isProcessing = false;
        }, 1000);
      }
    },

    // 清理资源
    async cleanup() {
      try {
        if (this.html5QrCode) {
          if (this.isScanning) {
            await this.html5QrCode.stop();
          }
          await this.html5QrCode.clear();
          this.html5QrCode = null;
        }
        this.isScanning = false;
        this.flashlightOn = false;
        this.supportsTorch = false;
        console.log(`[UnifiedScan] 资源清理完成`);
      } catch (error) {
        console.error(`[UnifiedScan] 清理资源失败:`, error);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100vh;
  background-color: #000;
  overflow: hidden;
}

.scan-box {
  position: relative;
  width: 100%;
  height: 100%;
}

.code-reader {
  width: 100%;
  height: 100%;
}

.tip-text {
  position: absolute;
  bottom: 150px;
  left: 50%;
  transform: translateX(-50%);
  color: white;
  text-align: center;
  z-index: 10;
}

.code-tip {
  margin-top: 10px;
  font-size: 14px;
}

.mode-restriction {
  background-color: rgba(0, 0, 0, 0.6);
  padding: 4px 8px;
  border-radius: 4px;
  color: #ffd700;
}

.success-effect {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.success-icon {
  width: 80px;
  height: 80px;
  background-color: #4caf50;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
}

.success-text {
  color: white;
  font-size: 18px;
  font-weight: bold;
}

.control-buttons {
  position: absolute;
  bottom: 80px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 15px;
  z-index: 10;
}

.camera-toggle-btn,
.flashlight-btn,
.manual-input-btn {
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 10px 15px;
  border-radius: 20px;
  font-size: 14px;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.manual-input-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.manual-input-dialog {
  background-color: white;
  padding: 20px;
  border-radius: 10px;
  width: 80%;
  max-width: 300px;
}

.dialog-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
  text-align: center;
}

.manual-input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  margin-bottom: 15px;
  font-size: 16px;
}

.dialog-buttons {
  display: flex;
  gap: 10px;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  padding: 10px;
  border: none;
  border-radius: 5px;
  font-size: 16px;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #333;
}

.confirm-btn {
  background-color: #1989fa;
  color: white;
}

.confirm-btn:disabled {
  background-color: #ccc;
  color: #999;
}
</style>
