<template>
  <view class="container">
    <!-- 扫码器容器 -->
    <view class="scan-box">
      <video
        :id="scannerId"
        class="code-reader"
        :style="{ width: '100%', height: '100%' }"
        autoplay
        muted
        playsinline
      ></video>

      <!-- 扫描提示 -->
      <view class="tip-text" v-if="!isScanning">
        <view>{{ scanTipText }}</view>
        <view class="code-tip">
          <span class="mode-restriction">{{ modeRestrictionText }}</span>
        </view>
      </view>
    </view>

    <!-- 成功效果 -->
    <view v-if="showSuccessEffect" class="success-effect">
      <view class="success-icon">
        <uni-icons type="checkmarkempty" size="60" color="#fff"></uni-icons>
      </view>
      <view class="success-text">扫码成功</view>
    </view>

    <!-- 控制按钮 -->
    <div class="control-buttons">
      <view class="camera-toggle-btn" @click="toggleCamera" v-if="isScanning">
        <text>切换摄像头</text>
      </view>

      <view
        class="flashlight-btn"
        @click="toggleFlashlight"
        v-if="isScanning && supportsTorch"
      >
        <text>{{ flashlightOn ? "关闭闪光灯" : "开启闪光灯" }}</text>
      </view>

      <!-- 手动输入按钮 (仅走访扫码显示) -->
      <view
        class="manual-input-btn"
        @click="showManualInput"
        v-if="scanMode === 'visit'"
      >
        <text>手动输入</text>
      </view>
    </div>

    <!-- 手动输入弹窗 (仅走访扫码) -->
    <view v-if="showManualInputDialog" class="manual-input-overlay">
      <view class="manual-input-dialog">
        <view class="dialog-title">手动输入条形码</view>
        <input
          v-model="manualCode"
          placeholder="请输入条形码"
          class="manual-input"
          type="text"
          @input="validateManualCode"
        />
        <view class="dialog-buttons">
          <button class="cancel-btn" @click="closeManualInput">取消</button>
          <button
            class="confirm-btn"
            @click="submitManualCode"
            :disabled="!isValidManualCode"
          >
            确定
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import {
  BrowserMultiFormatReader,
  BrowserMultiFormatOneDReader,
  BrowserQRCodeReader,
} from "@zxing/browser";
import { getQrcode } from "@/api";

export default {
  data() {
    return {
      codeReader: null,
      scanControls: null,
      isScanning: false,
      showSuccessEffect: false,
      lastScanTime: 0,
      isProcessing: false,
      isDestroyed: false,
      isInitializing: false,
      flashlightOn: false,
      supportsTorch: false,
      currentCameraId: null,
      availableCameras: [],
      currentCameraIndex: 0,

      // 扫码模式：'visit'(走访) 或 'inspect'(巡视)
      scanMode: "visit",

      // 手动输入相关 (仅走访扫码)
      manualCode: "",
      isValidManualCode: false,
      showManualInputDialog: false,
    };
  },

  computed: {
    // 扫码器ID
    scannerId() {
      return this.scanMode === "visit"
        ? "visit-barcode-reader"
        : "inspect-qr-reader";
    },

    // 扫码类型参数
    scanType() {
      return this.scanMode === "visit" ? "1" : "0";
    },

    // 扫描提示文本
    scanTipText() {
      return this.scanMode === "visit"
        ? "将条形码放入框内进行扫描"
        : "将二维码放入框内进行扫描";
    },

    // 模式限制文本
    modeRestrictionText() {
      return this.scanMode === "visit"
        ? "走访专用条形码扫描"
        : "巡视专用二维码扫描";
    },

    // 跳转页面URL
    targetPageUrl() {
      return this.scanMode === "visit"
        ? "/pages/visit-detail/index"
        : "/pages/inspect-detail/index";
    },

    // URL参数名称
    codeParamName() {
      return this.scanMode === "visit" ? "barcode" : "qrcode";
    },
  },

  onLoad(options) {
    console.log(`[UnifiedScan] 页面加载，参数:`, options);

    // 从URL参数获取扫码模式
    if (options.mode) {
      this.scanMode = options.mode;
    }

    console.log(`[UnifiedScan] 扫码模式: ${this.scanMode}`);
  },

  mounted() {
    console.log(`[UnifiedScan] mounted - 模式: ${this.scanMode}`);
    this.initializeScanner();
  },

  beforeUnmount() {
    console.log(`[UnifiedScan] beforeUnmount`);
    this.isDestroyed = true;
    this.cleanup();
  },

  onUnload() {
    console.log(`[UnifiedScan] onUnload`);
    this.isDestroyed = true;
    this.cleanup();
  },

  onHide() {
    console.log(`[UnifiedScan] onHide`);
    this.stopScanning();
  },

  onShow() {
    console.log(`[UnifiedScan] onShow`);
    if (!this.isScanning && !this.isDestroyed) {
      setTimeout(() => {
        this.initializeScanner();
      }, 500);
    }
  },

  methods: {
    // 获取对应的扫码器实例
    getCodeReader() {
      if (this.scanMode === "visit") {
        // 走访扫码使用一维条形码扫描器
        return new BrowserMultiFormatOneDReader();
      } else {
        // 巡视扫码使用二维码扫描器
        return new BrowserQRCodeReader();
      }
    },

    // 初始化扫码器
    async initializeScanner() {
      // 防止重复初始化
      if (this.isInitializing) {
        console.log(`[UnifiedScan] 正在初始化中，跳过重复调用`);
        return;
      }

      try {
        console.log(`[UnifiedScan] 初始化扫码器 - 模式: ${this.scanMode}`);
        this.isInitializing = true;

        // 如果已经有实例，先清理
        if (this.codeReader) {
          await this.cleanup();
        }

        // 创建对应的扫码器实例
        this.codeReader = this.getCodeReader();

        // 获取可用摄像头
        await this.getCameras();

        // 启动扫描
        await this.startScanning();
      } catch (error) {
        console.error(`[UnifiedScan] 初始化扫码器失败:`, error);
        uni.showToast({
          title: "扫码器初始化失败",
          icon: "none",
          duration: 2000,
        });
      } finally {
        this.isInitializing = false;
      }
    },

    // 获取可用摄像头
    async getCameras() {
      try {
        // 使用 @zxing/browser 的静态方法获取摄像头列表
        const { BrowserCodeReader } = await import("@zxing/browser");
        const cameras = await BrowserCodeReader.listVideoInputDevices();
        this.availableCameras = cameras || [];

        console.log(`[UnifiedScan] 获取到摄像头列表:`, this.availableCameras);

        // 多重策略优先选择后置摄像头
        let backCameraIndex = -1;

        // 策略1：明确包含"back"或"rear"或"environment"关键字的摄像头
        backCameraIndex = this.availableCameras.findIndex(
          (camera) =>
            camera.label &&
            (camera.label.toLowerCase().includes("back") ||
              camera.label.toLowerCase().includes("rear") ||
              camera.label.toLowerCase().includes("environment"))
        );

        // 策略2：如果策略1失败，选择索引为1的摄像头（通常是后置）
        if (backCameraIndex === -1 && this.availableCameras.length > 1) {
          backCameraIndex = 1;
        }

        // 策略3：如果都失败，使用第一个可用摄像头
        if (backCameraIndex === -1 && this.availableCameras.length > 0) {
          backCameraIndex = 0;
        }

        if (backCameraIndex !== -1) {
          this.currentCameraIndex = backCameraIndex;
          this.currentCameraId =
            this.availableCameras[backCameraIndex].deviceId;
          console.log(
            `[UnifiedScan] 选择摄像头:`,
            this.availableCameras[backCameraIndex]
          );
        } else {
          console.warn(`[UnifiedScan] 未找到可用摄像头`);
        }
      } catch (error) {
        console.error(`[UnifiedScan] 获取摄像头失败:`, error);
        uni.showToast({
          title: "获取摄像头失败",
          icon: "none",
          duration: 2000,
        });
      }
    },

    // 开始扫描
    async startScanning() {
      if (this.isScanning || this.isDestroyed || !this.codeReader) {
        return;
      }

      try {
        console.log(`[UnifiedScan] 开始扫描 - 模式: ${this.scanMode}`);
        this.isScanning = true;

        // 获取视频预览元素
        const previewElem = document.querySelector(`#${this.scannerId}`);
        if (!previewElem) {
          throw new Error("未找到视频预览元素");
        }

        // 扫码回调函数
        const scanCallback = (result, error) => {
          if (result) {
            // 扫码成功
            this.onScanSuccess(result.getText(), result);
          }
          // 错误处理（通常可以忽略，避免控制台日志过多）
          if (error && error.name !== "NotFoundException") {
            console.warn(`[UnifiedScan] 扫码错误:`, error);
          }
        };

        // 尝试启动扫描，使用回退策略
        this.scanControls = await this.tryStartScanning(
          previewElem,
          scanCallback
        );

        console.log(`[UnifiedScan] 扫描已启动`);

        // 检查是否支持闪光灯
        this.checkTorchSupport();
      } catch (error) {
        console.error(`[UnifiedScan] 启动扫描失败:`, error);
        this.isScanning = false;

        uni.showToast({
          title: "启动扫描失败，请检查摄像头权限",
          icon: "none",
          duration: 3000,
        });
      }
    },

    // 尝试启动扫描，使用回退策略
    async tryStartScanning(previewElem, scanCallback) {
      // 策略1：使用选定的摄像头设备ID
      if (this.currentCameraId) {
        try {
          console.log(
            `[UnifiedScan] 尝试使用选定摄像头:`,
            this.currentCameraId
          );
          const controls = await this.codeReader.decodeFromVideoDevice(
            this.currentCameraId,
            previewElem,
            scanCallback
          );
          return controls; // 成功则返回控制对象
        } catch (error) {
          console.warn(
            `[UnifiedScan] 选定摄像头启动失败，尝试其他摄像头:`,
            error
          );

          // 如果当前摄像头失败，尝试其他摄像头
          const controls = await this.tryOtherCameras(
            previewElem,
            scanCallback
          );
          if (controls) return controls;
        }
      }

      // 策略2：使用环境摄像头约束（后置摄像头）
      try {
        console.log(`[UnifiedScan] 尝试使用环境摄像头约束`);
        const controls = await this.codeReader.decodeFromVideoDevice(
          undefined, // 不指定设备ID，让浏览器选择
          previewElem,
          scanCallback
        );
        return controls;
      } catch (error) {
        console.warn(`[UnifiedScan] 环境摄像头约束失败:`, error);
      }

      // 策略3：使用第一个可用摄像头
      if (this.availableCameras.length > 0) {
        try {
          console.log(`[UnifiedScan] 尝试使用第一个可用摄像头`);
          const firstCamera = this.availableCameras[0];
          const controls = await this.codeReader.decodeFromVideoDevice(
            firstCamera.deviceId,
            previewElem,
            scanCallback
          );
          this.currentCameraId = firstCamera.deviceId;
          this.currentCameraIndex = 0;
          return controls;
        } catch (error) {
          console.error(`[UnifiedScan] 第一个摄像头启动失败:`, error);
        }
      }

      throw new Error("所有摄像头启动策略都失败");
    },

    // 尝试其他摄像头
    async tryOtherCameras(previewElem, scanCallback) {
      for (let i = 0; i < this.availableCameras.length; i++) {
        if (i === this.currentCameraIndex) continue; // 跳过已经尝试过的摄像头

        try {
          const camera = this.availableCameras[i];
          console.log(`[UnifiedScan] 尝试摄像头 ${i}:`, camera);

          const controls = await this.codeReader.decodeFromVideoDevice(
            camera.deviceId,
            previewElem,
            scanCallback
          );

          // 成功则更新当前摄像头
          this.currentCameraIndex = i;
          this.currentCameraId = camera.deviceId;
          console.log(`[UnifiedScan] 成功切换到摄像头 ${i}`);
          return controls;
        } catch (error) {
          console.warn(`[UnifiedScan] 摄像头 ${i} 启动失败:`, error);
        }
      }

      return null; // 所有摄像头都无法启动
    },

    // 停止扫描
    async stopScanning() {
      if (!this.isScanning || !this.scanControls) {
        return;
      }

      try {
        console.log(`[UnifiedScan] 停止扫描`);
        this.scanControls.stop();
        this.isScanning = false;
        this.flashlightOn = false;
        this.supportsTorch = false;
        this.scanControls = null;
      } catch (error) {
        console.error(`[UnifiedScan] 停止扫描失败:`, error);
      }
    },

    // 扫码成功处理
    async onScanSuccess(decodedText, result) {
      const currentTime = Date.now();

      // 防止重复扫描（500ms内）
      if (currentTime - this.lastScanTime < 500 || this.isProcessing) {
        return;
      }

      this.lastScanTime = currentTime;
      this.isProcessing = true;

      try {
        console.log(`[UnifiedScan] 扫码成功:`, decodedText);

        // 验证扫码结果格式
        if (!this.validateScanResult(decodedText)) {
          const codeType = this.scanMode === "visit" ? "条形码" : "二维码";
          uni.showToast({
            title: `请扫描有效的${codeType}`,
            icon: "none",
            duration: 2000,
          });
          return;
        }

        // 显示成功效果
        this.showSuccessEffect = true;
        setTimeout(() => {
          this.showSuccessEffect = false;
        }, 1500);

        // 调用业务API验证扫码结果
        const response = await getQrcode({
          qrcode: decodedText,
          type: this.scanType,
        });

        if (response && response.code === 200) {
          const codeType = this.scanMode === "visit" ? "条形码" : "二维码";
          console.log(`[UnifiedScan] ${codeType}验证成功:`, response.data);

          // 停止扫描
          await this.stopScanning();

          // 跳转到下一个页面，传递必要参数
          setTimeout(() => {
            uni.navigateTo({
              url: `${this.targetPageUrl}?${
                this.codeParamName
              }=${encodeURIComponent(decodedText)}&data=${encodeURIComponent(
                JSON.stringify(response.data)
              )}`,
            });
          }, 1000);
        } else {
          const codeType = this.scanMode === "visit" ? "条形码" : "二维码";
          throw new Error(response?.msg || `${codeType}验证失败`);
        }
      } catch (error) {
        console.error(`[UnifiedScan] 处理扫码结果失败:`, error);

        const codeType = this.scanMode === "visit" ? "条形码" : "二维码";
        uni.showToast({
          title: error.message || `${codeType}无效`,
          icon: "none",
          duration: 2000,
        });
      } finally {
        // 延迟重置处理状态，避免快速重复扫描
        setTimeout(() => {
          this.isProcessing = false;
        }, 1000);
      }
    },

    // 验证扫码结果格式
    validateScanResult(code) {
      if (!code || typeof code !== "string") {
        return false;
      }

      if (this.scanMode === "visit") {
        // 走访扫码：验证条形码格式（通常是数字）
        return /^[0-9]{8,}$/.test(code.trim());
      } else {
        // 巡视扫码：二维码格式验证（更宽松）
        return code.trim().length > 0;
      }
    },

    // 检查闪光灯支持
    async checkTorchSupport() {
      try {
        // @zxing/browser 的闪光灯支持检查
        if (
          this.scanControls &&
          this.scanControls.streamVideoConstraintsApply
        ) {
          // 尝试检查是否支持 torch 约束
          const stream = this.scanControls.stream;
          if (stream) {
            const track = stream.getVideoTracks()[0];
            if (track && track.getCapabilities) {
              const capabilities = track.getCapabilities();
              this.supportsTorch = capabilities && capabilities.torch === true;
              console.log(`[UnifiedScan] 闪光灯支持:`, this.supportsTorch);
            }
          }
        }
      } catch (error) {
        console.warn(`[UnifiedScan] 检查闪光灯支持失败:`, error);
        this.supportsTorch = false;
      }
    },

    // 切换摄像头
    async toggleCamera() {
      if (!this.isScanning || this.availableCameras.length <= 1) {
        return;
      }

      try {
        console.log(`[UnifiedScan] 切换摄像头`);

        // 停止当前扫描
        await this.stopScanning();

        // 切换到下一个摄像头
        this.currentCameraIndex =
          (this.currentCameraIndex + 1) % this.availableCameras.length;
        this.currentCameraId =
          this.availableCameras[this.currentCameraIndex].deviceId;

        console.log(
          `[UnifiedScan] 切换到摄像头:`,
          this.availableCameras[this.currentCameraIndex]
        );

        // 重新启动扫描
        await this.startScanning();
      } catch (error) {
        console.error(`[UnifiedScan] 切换摄像头失败:`, error);
        uni.showToast({
          title: "切换摄像头失败",
          icon: "none",
          duration: 2000,
        });
      }
    },

    // 切换闪光灯
    async toggleFlashlight() {
      if (!this.supportsTorch || !this.scanControls) {
        return;
      }

      try {
        // @zxing/browser 的闪光灯控制
        const stream = this.scanControls.stream;
        if (stream) {
          const track = stream.getVideoTracks()[0];
          if (track && track.applyConstraints) {
            if (this.flashlightOn) {
              await track.applyConstraints({
                advanced: [{ torch: false }],
              });
              this.flashlightOn = false;
              console.log(`[UnifiedScan] 闪光灯已关闭`);
            } else {
              await track.applyConstraints({
                advanced: [{ torch: true }],
              });
              this.flashlightOn = true;
              console.log(`[UnifiedScan] 闪光灯已开启`);
            }
          }
        }
      } catch (error) {
        console.error(`[UnifiedScan] 切换闪光灯失败:`, error);
        uni.showToast({
          title: "闪光灯操作失败",
          icon: "none",
          duration: 2000,
        });
      }
    },

    // 显示手动输入弹窗 (仅走访扫码)
    showManualInput() {
      if (this.scanMode !== "visit") {
        return;
      }
      this.showManualInputDialog = true;
      this.manualCode = "";
      this.isValidManualCode = false;
    },

    // 关闭手动输入弹窗
    closeManualInput() {
      this.showManualInputDialog = false;
      this.manualCode = "";
      this.isValidManualCode = false;
    },

    // 验证手动输入的条形码
    validateManualCode() {
      const code = this.manualCode.trim();
      this.isValidManualCode = /^[0-9]{8,}$/.test(code);
    },

    // 提交手动输入的条形码
    async submitManualCode() {
      if (!this.isValidManualCode || this.isProcessing) {
        return;
      }

      this.isProcessing = true;

      try {
        console.log(`[UnifiedScan] 手动输入条形码:`, this.manualCode);

        // 关闭弹窗
        this.closeManualInput();

        // 显示成功效果
        this.showSuccessEffect = true;
        setTimeout(() => {
          this.showSuccessEffect = false;
        }, 1500);

        // 调用业务API验证条形码
        const response = await getQrcode({
          qrcode: this.manualCode,
          type: this.scanType,
        });

        if (response && response.code === 200) {
          console.log(`[UnifiedScan] 手动输入条形码验证成功:`, response.data);

          // 停止扫描
          await this.stopScanning();

          // 跳转到下一个页面，传递必要参数
          setTimeout(() => {
            uni.navigateTo({
              url: `${this.targetPageUrl}?${
                this.codeParamName
              }=${encodeURIComponent(
                this.manualCode
              )}&data=${encodeURIComponent(JSON.stringify(response.data))}`,
            });
          }, 1000);
        } else {
          throw new Error(response?.msg || "条形码验证失败");
        }
      } catch (error) {
        console.error(`[UnifiedScan] 手动输入条形码处理失败:`, error);

        uni.showToast({
          title: error.message || "条形码无效",
          icon: "none",
          duration: 2000,
        });
      } finally {
        // 延迟重置处理状态
        setTimeout(() => {
          this.isProcessing = false;
        }, 1000);
      }
    },

    // 清理资源
    async cleanup() {
      try {
        if (this.scanControls) {
          this.scanControls.stop();
          this.scanControls = null;
        }
        if (this.codeReader) {
          // @zxing/browser 不需要显式清理，但我们可以重置实例
          this.codeReader = null;
        }
        this.isScanning = false;
        this.flashlightOn = false;
        this.supportsTorch = false;
        console.log(`[UnifiedScan] 资源清理完成`);
      } catch (error) {
        console.error(`[UnifiedScan] 清理资源失败:`, error);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100vh;
  background-color: #000;
  overflow: hidden;
}

.scan-box {
  position: relative;
  width: 100%;
  height: 100%;
}

.code-reader {
  width: 100%;
  height: 100%;
}

.tip-text {
  position: absolute;
  bottom: 150px;
  left: 50%;
  transform: translateX(-50%);
  color: white;
  text-align: center;
  z-index: 10;
}

.code-tip {
  margin-top: 10px;
  font-size: 14px;
}

.mode-restriction {
  background-color: rgba(0, 0, 0, 0.6);
  padding: 4px 8px;
  border-radius: 4px;
  color: #ffd700;
}

.success-effect {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.success-icon {
  width: 80px;
  height: 80px;
  background-color: #4caf50;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
}

.success-text {
  color: white;
  font-size: 18px;
  font-weight: bold;
}

.control-buttons {
  position: absolute;
  bottom: 80px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 15px;
  z-index: 10;
}

.camera-toggle-btn,
.flashlight-btn,
.manual-input-btn {
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 10px 15px;
  border-radius: 20px;
  font-size: 14px;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.manual-input-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.manual-input-dialog {
  background-color: white;
  padding: 20px;
  border-radius: 10px;
  width: 80%;
  max-width: 300px;
}

.dialog-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
  text-align: center;
}

.manual-input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  margin-bottom: 15px;
  font-size: 16px;
}

.dialog-buttons {
  display: flex;
  gap: 10px;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  padding: 10px;
  border: none;
  border-radius: 5px;
  font-size: 16px;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #333;
}

.confirm-btn {
  background-color: #1989fa;
  color: white;
}

.confirm-btn:disabled {
  background-color: #ccc;
  color: #999;
}
</style>
